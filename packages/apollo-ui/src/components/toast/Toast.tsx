import { Ref } from "react"
import { Toast as BaseToast } from "@base-ui-components/react"
import { cva } from "class-variance-authority"
import classNames from "classnames"

import { useToast } from "../../hooks"
import { Alert } from "../alert"
import styles from "./toast.module.css"
import { ToastProps } from "./ToastProps"

const toasPositionVariants = cva(styles.toastRoot, {
  variants: {
    position: {
      "top-center": styles.toastRootTopCenter,
      "top-right": styles.toastRootTopRight,
      "top-left": styles.toastRootTopLeft,
      "bottom-center": styles.toastRootBottomCenter,
      "bottom-right": styles.toastRootBottomRight,
      "bottom-left": styles.toastRootBottomLeft,
    },
    width: {
      default: styles.toastRootDefaultWidth,
      full: styles.toastRootFullWidth,
    },
  },
  defaultVariants: {
    position: "top-right",
    width: "default",
  },
})

export function Toast({ toast, ...toastRootProps }: ToastProps) {
  const { close } = useToast()
  return (
    <BaseToast.Root
      {...toastRootProps}
      toast={toast}
      className={classNames(
        "ApolloToast-root",
        toasPositionVariants({
          position: toast.position ?? "top-right",
          width: toast.fullWidth ? "full" : "default",
        }),
        toast.className
      )}
    >
      <Alert
        color={toast.type}
        title={toast.title}
        description={toast.description}
        startDecorator={toast.startDecorator}
        endDecorator={toast.endDecorator}
        onClose={
          toast.onClose || toast.isClosable ? () => close(toast.id) : undefined
        }
        fullWidth={toast.fullWidth}
        style={toast.style}
        className={classNames("ApolloToast-alert", toast.className)}
        ref={toast.ref as Ref<HTMLDivElement>}
      />
    </BaseToast.Root>
  )
}
